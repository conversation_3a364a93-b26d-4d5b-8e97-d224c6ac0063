<template>
  <div class="theme-toggle">
    <el-dropdown @command="handleThemeChange" trigger="click">
      <el-button circle>
        <font-awesome-icon :icon="themeIcon" />
      </el-button>
      
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            command="light" 
            :class="{ active: currentTheme === 'light' }"
          >
            <font-awesome-icon :icon="['fas', 'sun']" />
            亮色模式
          </el-dropdown-item>
          
          <el-dropdown-item 
            command="dark" 
            :class="{ active: currentTheme === 'dark' }"
          >
            <font-awesome-icon :icon="['fas', 'moon']" />
            暗色模式
          </el-dropdown-item>
          
          <el-dropdown-item 
            command="auto" 
            :class="{ active: currentTheme === 'auto' }"
          >
            <font-awesome-icon :icon="['fas', 'circle-half-stroke']" />
            跟随系统
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getCurrentTheme, setTheme, initTheme, forceRefreshTheme, type ThemeMode } from '@/utils/theme'

const currentTheme = ref<ThemeMode>('auto')
let cleanupWatcher: (() => void) | null = null

// 计算当前主题图标
const themeIcon = computed(() => {
  if (currentTheme.value === 'auto') {
    return ['fas', 'circle-half-stroke']
  }
  return currentTheme.value === 'dark' ? ['fas', 'moon'] : ['fas', 'sun']
})

// 处理主题切换
const handleThemeChange = (theme: ThemeMode) => {
  currentTheme.value = theme
  setTheme(theme)

  // 延迟强制刷新，确保主题变量生效
  setTimeout(() => {
    forceRefreshTheme()
  }, 100)
}

onMounted(() => {
  // 初始化主题
  currentTheme.value = getCurrentTheme()
  cleanupWatcher = initTheme()
})

onUnmounted(() => {
  // 清理监听器
  if (cleanupWatcher) {
    cleanupWatcher()
  }
})
</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.theme-toggle {
  .el-button {
    border: none;
    background: transparent;
    
    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }
  
  :deep(.el-dropdown-menu__item) {
    display: flex;
    align-items: center;
    gap: 8px;
    
    &.active {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
    
    svg {
      width: 14px;
      height: 14px;
    }
  }
}
</style>
