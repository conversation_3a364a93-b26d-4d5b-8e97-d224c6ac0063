import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue'),
    meta: {
      title: '关于'
    }
  },
  {
    path: '/icon-test',
    name: 'IconTest',
    component: () => import('@/views/IconTest.vue'),
    meta: {
      title: '图标测试'
    }
  },
  {
    path: '/theme-demo',
    name: 'ThemeDemo',
    component: () => import('@/views/ThemeDemo.vue'),
    meta: {
      title: '主题演示'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Vue Template`
  }
  next()
})

export default router
