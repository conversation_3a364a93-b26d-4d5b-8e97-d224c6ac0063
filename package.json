{"name": "vue-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fingerprintjs/fingerprintjs": "^4.6.2", "@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/vue-fontawesome": "^3.1.1", "animate.css": "^4.1.1", "axios": "^1.11.0", "element-plus": "^2.10.5", "jsencrypt": "^3.3.2", "pinia": "^3.0.3", "sass": "^1.89.2", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}