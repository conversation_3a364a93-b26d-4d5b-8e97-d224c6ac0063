<template>
  <div class="home-container">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>Vue3 + Element Plus 示例</span>
          <font-awesome-icon :icon="['fas', 'home']" />
        </div>
      </template>
      
      <div class="demo-content">
        <h3>计数器示例 (Pinia)</h3>
        <p>当前计数: {{ count }}</p>
        <p>双倍计数: {{ doubleCount }}</p>
        <p>是否为偶数: {{ isEven ? '是' : '否' }}</p>
        
        <div class="button-group">
          <el-button type="primary" @click="increment">
            <font-awesome-icon :icon="['fas', 'plus']" />
            增加
          </el-button>
          <el-button type="danger" @click="decrement">减少</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
        
        <div class="mt-4">
          <el-input 
            v-model.number="inputValue" 
            placeholder="输入数字" 
            type="number"
            style="width: 200px; margin-right: 10px;"
          />
          <el-button @click="setCount(inputValue)">设置计数</el-button>
        </div>
        
        <div class="mt-4">
          <el-button type="success" @click="showFingerprint">
            获取浏览器指纹
          </el-button>
          <p v-if="fingerprint">指纹ID: {{ fingerprint }}</p>
        </div>
      </div>
    </el-card>

    <!-- 组件示例 -->
    <DemoComponent />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useCounterStore } from '@/store/counter'
import { getBrowserFingerprint } from '@/utils/fingerprint'
import { ElMessage } from 'element-plus'
import DemoComponent from '@/components/DemoComponent.vue'

const counterStore = useCounterStore()
const { count, doubleCount, isEven } = storeToRefs(counterStore)
const { increment, decrement, reset, setCount } = counterStore

const inputValue = ref(0)
const fingerprint = ref('')

const showFingerprint = async () => {
  try {
    fingerprint.value = await getBrowserFingerprint()
    ElMessage.success('获取指纹成功')
  } catch (error) {
    ElMessage.error('获取指纹失败')
  }
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.home-container {
  padding: $spacing-lg;
}

.demo-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-content {
  text-align: center;
}

.button-group {
  margin: $spacing-md 0;
  
  .el-button {
    margin: 0 $spacing-xs;
  }
}

.mt-4 {
  margin-top: $spacing-md;
}
</style>
