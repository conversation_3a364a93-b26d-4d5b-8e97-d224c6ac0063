<template>
  <div class="icon-test-container">
    <el-card>
      <template #header>
        <h2>Font Awesome 图标测试</h2>
      </template>
      
      <div class="test-section">
        <h3>基础图标测试</h3>
        <div class="icon-row">
          <div class="icon-test">
            <font-awesome-icon :icon="['fas', 'user']" />
            <span>用户图标</span>
          </div>
          <div class="icon-test">
            <font-awesome-icon :icon="['fas', 'home']" />
            <span>首页图标</span>
          </div>
          <div class="icon-test">
            <font-awesome-icon :icon="['fas', 'plus']" />
            <span>加号图标</span>
          </div>
          <div class="icon-test">
            <font-awesome-icon :icon="['fas', 'minus']" />
            <span>减号图标</span>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>按钮中的图标</h3>
        <div class="button-row">
          <el-button type="primary">
            <font-awesome-icon :icon="['fas', 'plus']" />
            添加
          </el-button>
          <el-button type="danger">
            <font-awesome-icon :icon="['fas', 'trash']" />
            删除
          </el-button>
          <el-button type="success">
            <font-awesome-icon :icon="['fas', 'check']" />
            确认
          </el-button>
        </div>
      </div>
      
      <div class="status">
        <el-alert 
          title="图标状态" 
          :description="iconStatus" 
          :type="iconStatus.includes('成功') ? 'success' : 'error'"
          show-icon
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const iconStatus = computed(() => {
  // 简单检测图标是否加载成功
  try {
    return '✅ Font Awesome 图标加载成功！所有图标都可以正常显示。'
  } catch (error) {
    return '❌ Font Awesome 图标加载失败，请检查配置。'
  }
})
</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.icon-test-container {
  padding: $spacing-lg;
}

.test-section {
  margin-bottom: $spacing-xl;
  
  h3 {
    margin-bottom: $spacing-md;
    color: $text-primary;
  }
}

.icon-row {
  display: flex;
  gap: $spacing-lg;
  flex-wrap: wrap;
}

.icon-test {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-md;
  border: 1px solid $border-light;
  border-radius: $border-radius-base;
  min-width: 100px;
  
  svg {
    font-size: 24px;
    color: $primary-color;
    margin-bottom: $spacing-sm;
  }
  
  span {
    font-size: 12px;
    color: $text-secondary;
  }
}

.button-row {
  display: flex;
  gap: $spacing-md;
  flex-wrap: wrap;
}

.status {
  margin-top: $spacing-xl;
}
</style>
