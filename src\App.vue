<template>
  <div id="app">
    <el-container>
      <el-header class="app-header">
        <div class="header-content">
          <h1>Vue3 Template</h1>
          <div class="header-right">
            <el-menu
              mode="horizontal"
              :default-active="$route.path"
              router
              class="header-menu"
            >
            <el-menu-item index="/">
              <font-awesome-icon :icon="['fas', 'home']" />
              首页
            </el-menu-item>
            <el-menu-item index="/about">
              <font-awesome-icon :icon="['fas', 'cog']" />
              关于
            </el-menu-item>
            <el-menu-item index="/icon-test">
              <font-awesome-icon :icon="['fas', 'eye']" />
              图标测试
            </el-menu-item>
            <el-menu-item index="/theme-demo">
              <font-awesome-icon :icon="['fas', 'sun']" />
              主题演示
            </el-menu-item>
            </el-menu>

            <ThemeToggle />
          </div>
        </div>
      </el-header>

      <el-main class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import ThemeToggle from '@/components/ThemeToggle.vue'
import { onMounted } from 'vue'
import { initTheme } from '@/utils/theme'

// 初始化主题
onMounted(() => {
  initTheme()
})
</script>

<style lang="scss">
@import "@/styles/variables.scss";

#app {
  min-height: 100vh;
}

.app-header {
  background-color: $bg-color;
  border-bottom: 1px solid $border-base;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  h1 {
    margin: 0;
    color: var(--el-color-primary);
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.header-menu {
  border-bottom: none;
}

.app-main {
  padding: 0;
  background-color: $bg-color-page;
}

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
