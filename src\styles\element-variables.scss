// Element Plus 主题变量定制
// 这个文件会在所有 SCSS 文件中自动注入

// 引入我们的基础变量
@import './variables.scss';

// 覆盖 Element Plus 的 SCSS 变量
// 这些变量会在 Element Plus 编译时生效

// 主色调
$el-color-primary: $primary-color !default;
$el-color-success: $success-color !default;
$el-color-warning: $warning-color !default;
$el-color-danger: $danger-color !default;
$el-color-error: $danger-color !default;
$el-color-info: $info-color !default;

// 文字颜色
$el-text-color-primary: $text-primary !default;
$el-text-color-regular: $text-regular !default;
$el-text-color-secondary: $text-secondary !default;
$el-text-color-placeholder: $text-placeholder !default;

// 边框颜色
$el-border-color: $border-base !default;
$el-border-color-light: $border-light !default;
$el-border-color-lighter: $border-lighter !default;
$el-border-color-extra-light: $border-extra-light !default;

// 背景色
$el-bg-color: $bg-color !default;
$el-bg-color-page: $bg-color-page !default;
$el-bg-color-overlay: $bg-color-overlay !default;

// 圆角
$el-border-radius-base: $border-radius-base !default;
$el-border-radius-small: $border-radius-small !default;
$el-border-radius-round: $border-radius-round !default;
$el-border-radius-circle: $border-radius-circle !default;

// 阴影
$el-box-shadow: $box-shadow-base !default;
$el-box-shadow-light: $box-shadow-light !default;
$el-box-shadow-dark: $box-shadow-dark !default;
