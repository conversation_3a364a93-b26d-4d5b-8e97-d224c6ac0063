// Element Plus 主题变量覆盖
// 这个文件用于覆盖 Element Plus 的默认主题变量

// 引入我们的自定义变量
@import './variables.scss';

// 覆盖 Element Plus 的主题变量
// 主色调
$el-color-primary: $primary-color;
$el-color-success: $success-color;
$el-color-warning: $warning-color;
$el-color-danger: $danger-color;
$el-color-info: $info-color;

// 文字颜色
$el-text-color-primary: $text-primary;
$el-text-color-regular: $text-regular;
$el-text-color-secondary: $text-secondary;
$el-text-color-placeholder: $text-placeholder;

// 边框颜色
$el-border-color: $border-base;
$el-border-color-light: $border-light;
$el-border-color-lighter: $border-lighter;
$el-border-color-extra-light: $border-extra-light;

// 背景色
$el-bg-color: $bg-color;
$el-bg-color-page: $bg-color-page;
$el-bg-color-overlay: $bg-color-overlay;

// 圆角
$el-border-radius-base: $border-radius-base;
$el-border-radius-small: $border-radius-small;
$el-border-radius-round: $border-radius-round;
$el-border-radius-circle: $border-radius-circle;

// 阴影
$el-box-shadow: $box-shadow-base;
$el-box-shadow-light: $box-shadow-light;
$el-box-shadow-dark: $box-shadow-dark;

// 间距
$el-component-size-large: 40px;
$el-component-size: 32px;
$el-component-size-small: 24px;

// 字体大小
$el-font-size-extra-large: 20px;
$el-font-size-large: 18px;
$el-font-size-medium: 16px;
$el-font-size-base: 14px;
$el-font-size-small: 13px;
$el-font-size-extra-small: 12px;

// CSS 变量定义
:root {
  // 亮色模式（默认）
  --el-color-primary: #{$primary-color};
  --el-color-primary-light-3: #{mix(white, $primary-color, 30%)};
  --el-color-primary-light-5: #{mix(white, $primary-color, 50%)};
  --el-color-primary-light-7: #{mix(white, $primary-color, 70%)};
  --el-color-primary-light-8: #{mix(white, $primary-color, 80%)};
  --el-color-primary-light-9: #{mix(white, $primary-color, 90%)};
  --el-color-primary-dark-2: #{mix(black, $primary-color, 20%)};

  --el-color-success: #{$success-color};
  --el-color-warning: #{$warning-color};
  --el-color-danger: #{$danger-color};
  --el-color-info: #{$info-color};

  --el-text-color-primary: #{$text-primary};
  --el-text-color-regular: #{$text-regular};
  --el-text-color-secondary: #{$text-secondary};
  --el-text-color-placeholder: #{$text-placeholder};

  --el-border-color: #{$border-base};
  --el-border-color-light: #{$border-light};
  --el-border-color-lighter: #{$border-lighter};
  --el-border-color-extra-light: #{$border-extra-light};

  --el-bg-color: #{$bg-color};
  --el-bg-color-page: #{$bg-color-page};
  --el-bg-color-overlay: #{$bg-color-overlay};
}

// 暗色模式
[data-theme="dark"] {
  --el-color-primary: #{lighten($primary-color, 10%)};
  --el-color-primary-light-3: #{mix(white, lighten($primary-color, 10%), 30%)};
  --el-color-primary-light-5: #{mix(white, lighten($primary-color, 10%), 50%)};
  --el-color-primary-light-7: #{mix(white, lighten($primary-color, 10%), 70%)};
  --el-color-primary-light-8: #{mix(white, lighten($primary-color, 10%), 80%)};
  --el-color-primary-light-9: #{mix(white, lighten($primary-color, 10%), 90%)};
  --el-color-primary-dark-2: #{mix(black, lighten($primary-color, 10%), 20%)};

  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-info: #909399;

  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;

  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;

  --el-bg-color: #141414;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color-overlay: #1d1e1f;

  --el-fill-color: #262727;
  --el-fill-color-light: #1f2021;
  --el-fill-color-lighter: #191a1b;
  --el-fill-color-extra-light: #131314;
  --el-fill-color-dark: #303133;
  --el-fill-color-darker: #424243;
  --el-fill-color-blank: transparent;
}

// 自动暗色模式（根据系统设置）
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --el-color-primary: #{lighten($primary-color, 10%)};
    --el-color-success: #67c23a;
    --el-color-warning: #e6a23c;
    --el-color-danger: #f56c6c;
    --el-color-info: #909399;

    --el-text-color-primary: #e5eaf3;
    --el-text-color-regular: #cfd3dc;
    --el-text-color-secondary: #a3a6ad;
    --el-text-color-placeholder: #8d9095;

    --el-border-color: #4c4d4f;
    --el-border-color-light: #414243;
    --el-border-color-lighter: #363637;
    --el-border-color-extra-light: #2b2b2c;

    --el-bg-color: #141414;
    --el-bg-color-page: #0a0a0a;
    --el-bg-color-overlay: #1d1e1f;
  }
}
