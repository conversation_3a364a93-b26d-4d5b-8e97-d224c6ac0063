// Element Plus 主题变量覆盖
// 这个文件用于覆盖 Element Plus 的默认主题变量

// 引入我们的自定义变量
@import './variables.scss';

// 覆盖 Element Plus 的主题变量
// 主色调
$el-color-primary: $primary-color;
$el-color-success: $success-color;
$el-color-warning: $warning-color;
$el-color-danger: $danger-color;
$el-color-info: $info-color;

// 文字颜色
$el-text-color-primary: $text-primary;
$el-text-color-regular: $text-regular;
$el-text-color-secondary: $text-secondary;
$el-text-color-placeholder: $text-placeholder;

// 边框颜色
$el-border-color: $border-base;
$el-border-color-light: $border-light;
$el-border-color-lighter: $border-lighter;
$el-border-color-extra-light: $border-extra-light;

// 背景色
$el-bg-color: $bg-color;
$el-bg-color-page: $bg-color-page;
$el-bg-color-overlay: $bg-color-overlay;

// 圆角
$el-border-radius-base: $border-radius-base;
$el-border-radius-small: $border-radius-small;
$el-border-radius-round: $border-radius-round;
$el-border-radius-circle: $border-radius-circle;

// 阴影
$el-box-shadow: $box-shadow-base;
$el-box-shadow-light: $box-shadow-light;
$el-box-shadow-dark: $box-shadow-dark;

// 间距
$el-component-size-large: 40px;
$el-component-size: 32px;
$el-component-size-small: 24px;

// 字体大小
$el-font-size-extra-large: 20px;
$el-font-size-large: 18px;
$el-font-size-medium: 16px;
$el-font-size-base: 14px;
$el-font-size-small: 13px;
$el-font-size-extra-small: 12px;

// CSS 变量定义 - 使用 !important 确保优先级
:root {
  // 亮色模式（默认）
  --el-color-primary: #{$primary-color} !important;
  --el-color-primary-light-3: #{mix(white, $primary-color, 30%)} !important;
  --el-color-primary-light-5: #{mix(white, $primary-color, 50%)} !important;
  --el-color-primary-light-7: #{mix(white, $primary-color, 70%)} !important;
  --el-color-primary-light-8: #{mix(white, $primary-color, 80%)} !important;
  --el-color-primary-light-9: #{mix(white, $primary-color, 90%)} !important;
  --el-color-primary-dark-2: #{mix(black, $primary-color, 20%)} !important;

  --el-color-success: #{$success-color} !important;
  --el-color-warning: #{$warning-color} !important;
  --el-color-danger: #{$danger-color} !important;
  --el-color-info: #{$info-color} !important;

  --el-text-color-primary: #{$text-primary} !important;
  --el-text-color-regular: #{$text-regular} !important;
  --el-text-color-secondary: #{$text-secondary} !important;
  --el-text-color-placeholder: #{$text-placeholder} !important;

  --el-border-color: #{$border-base} !important;
  --el-border-color-light: #{$border-light} !important;
  --el-border-color-lighter: #{$border-lighter} !important;
  --el-border-color-extra-light: #{$border-extra-light} !important;

  --el-bg-color: #{$bg-color} !important;
  --el-bg-color-page: #{$bg-color-page} !important;
  --el-bg-color-overlay: #{$bg-color-overlay} !important;
}

// 暗色模式
[data-theme="dark"] {
  --el-color-primary: #{lighten($primary-color, 10%)} !important;
  --el-color-primary-light-3: #{mix(white, lighten($primary-color, 10%), 30%)} !important;
  --el-color-primary-light-5: #{mix(white, lighten($primary-color, 10%), 50%)} !important;
  --el-color-primary-light-7: #{mix(white, lighten($primary-color, 10%), 70%)} !important;
  --el-color-primary-light-8: #{mix(white, lighten($primary-color, 10%), 80%)} !important;
  --el-color-primary-light-9: #{mix(white, lighten($primary-color, 10%), 90%)} !important;
  --el-color-primary-dark-2: #{mix(black, lighten($primary-color, 10%), 20%)} !important;

  --el-color-success: #67c23a !important;
  --el-color-warning: #e6a23c !important;
  --el-color-danger: #f56c6c !important;
  --el-color-info: #909399 !important;

  --el-text-color-primary: #e5eaf3 !important;
  --el-text-color-regular: #cfd3dc !important;
  --el-text-color-secondary: #a3a6ad !important;
  --el-text-color-placeholder: #8d9095 !important;

  --el-border-color: #4c4d4f !important;
  --el-border-color-light: #414243 !important;
  --el-border-color-lighter: #363637 !important;
  --el-border-color-extra-light: #2b2b2c !important;

  --el-bg-color: #141414 !important;
  --el-bg-color-page: #0a0a0a !important;
  --el-bg-color-overlay: #1d1e1f !important;

  --el-fill-color: #262727 !important;
  --el-fill-color-light: #1f2021 !important;
  --el-fill-color-lighter: #191a1b !important;
  --el-fill-color-extra-light: #131314 !important;
  --el-fill-color-dark: #303133 !important;
  --el-fill-color-darker: #424243 !important;
  --el-fill-color-blank: transparent !important;
}

// 自动暗色模式（根据系统设置）
@media (prefers-color-scheme: dark) {
  :root {
    --el-color-primary: #{lighten($primary-color, 10%)} !important;
    --el-color-success: #67c23a !important;
    --el-color-warning: #e6a23c !important;
    --el-color-danger: #f56c6c !important;
    --el-color-info: #909399 !important;

    --el-text-color-primary: #e5eaf3 !important;
    --el-text-color-regular: #cfd3dc !important;
    --el-text-color-secondary: #a3a6ad !important;
    --el-text-color-placeholder: #8d9095 !important;

    --el-border-color: #4c4d4f !important;
    --el-border-color-light: #414243 !important;
    --el-border-color-lighter: #363637 !important;
    --el-border-color-extra-light: #2b2b2c !important;

    --el-bg-color: #141414 !important;
    --el-bg-color-page: #0a0a0a !important;
    --el-bg-color-overlay: #1d1e1f !important;
  }
}

// 强制覆盖 Element Plus 组件样式
.el-button--primary {
  --el-button-bg-color: #{$primary-color} !important;
  --el-button-border-color: #{$primary-color} !important;
  --el-button-hover-bg-color: #{mix(white, $primary-color, 20%)} !important;
  --el-button-hover-border-color: #{mix(white, $primary-color, 20%)} !important;
  --el-button-active-bg-color: #{mix(black, $primary-color, 10%)} !important;
  --el-button-active-border-color: #{mix(black, $primary-color, 10%)} !important;
}

.el-menu-item.is-active {
  color: #{$primary-color} !important;
}

.el-menu--horizontal .el-menu-item.is-active {
  border-bottom-color: #{$primary-color} !important;
}

// 确保主色调在所有地方都生效
.el-link--primary {
  color: #{$primary-color} !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #{$primary-color} !important;
  border-color: #{$primary-color} !important;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #{$primary-color} !important;
  background: #{$primary-color} !important;
}

.el-input__inner:focus {
  border-color: #{$primary-color} !important;
}

.el-switch.is-checked .el-switch__core {
  background-color: #{$primary-color} !important;
}
