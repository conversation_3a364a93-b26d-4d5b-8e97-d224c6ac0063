<template>
  <div class="about-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>关于页面</span>
          <font-awesome-icon :icon="['fas', 'cog']" />
        </div>
      </template>
      
      <div class="about-content">
        <h2>技术栈</h2>
        <el-tag v-for="tech in techStack" :key="tech" type="primary" class="tech-tag">
          {{ tech }}
        </el-tag>
        
        <div class="mt-4">
          <el-button type="primary" @click="goHome">
            <font-awesome-icon :icon="['fas', 'home']" />
            返回首页
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const techStack = [
  'Vue 3',
  'TypeScript',
  'Vite',
  'Element Plus',
  'Pinia',
  'Vue Router',
  'Axios',
  'SCSS',
  'Font Awesome',
  'Animate.css'
]

const goHome = () => {
  router.push('/')
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.about-container {
  padding: $spacing-lg;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.about-content {
  text-align: center;
}

.tech-tag {
  margin: $spacing-xs;
}

.mt-4 {
  margin-top: $spacing-md;
}
</style>
