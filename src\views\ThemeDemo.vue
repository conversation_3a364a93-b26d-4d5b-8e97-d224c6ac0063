<template>
  <div class="theme-demo-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>主题演示</h2>
          <ThemeToggle />
        </div>
      </template>
      
      <div class="demo-section">
        <h3>颜色演示</h3>
        <div class="color-grid">
          <div class="color-item">
            <div class="color-box primary"></div>
            <span>主色调 (Primary)</span>
          </div>
          <div class="color-item">
            <div class="color-box success"></div>
            <span>成功色 (Success)</span>
          </div>
          <div class="color-item">
            <div class="color-box warning"></div>
            <span>警告色 (Warning)</span>
          </div>
          <div class="color-item">
            <div class="color-box danger"></div>
            <span>危险色 (Danger)</span>
          </div>
          <div class="color-item">
            <div class="color-box info"></div>
            <span>信息色 (Info)</span>
          </div>
        </div>
      </div>
      
      <el-divider />
      
      <div class="demo-section">
        <h3>组件演示</h3>
        <div class="component-grid">
          <div class="component-item">
            <h4>按钮</h4>
            <div class="button-group">
              <el-button type="primary">主要按钮</el-button>
              <el-button type="success">成功按钮</el-button>
              <el-button type="warning">警告按钮</el-button>
              <el-button type="danger">危险按钮</el-button>
              <el-button type="info">信息按钮</el-button>
            </div>
          </div>
          
          <div class="component-item">
            <h4>表单</h4>
            <el-form :model="form" label-width="80px">
              <el-form-item label="用户名">
                <el-input v-model="form.username" placeholder="请输入用户名" />
              </el-form-item>
              <el-form-item label="密码">
                <el-input v-model="form.password" type="password" placeholder="请输入密码" />
              </el-form-item>
              <el-form-item label="性别">
                <el-radio-group v-model="form.gender">
                  <el-radio value="male">男</el-radio>
                  <el-radio value="female">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="component-item">
            <h4>消息提示</h4>
            <div class="message-buttons">
              <el-button @click="showMessage('success')">成功消息</el-button>
              <el-button @click="showMessage('warning')">警告消息</el-button>
              <el-button @click="showMessage('error')">错误消息</el-button>
              <el-button @click="showMessage('info')">信息消息</el-button>
            </div>
          </div>
        </div>
      </div>
      
      <el-divider />
      
      <div class="demo-section">
        <h3>当前主题信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前主题">{{ currentThemeText }}</el-descriptions-item>
          <el-descriptions-item label="系统偏好">{{ systemPreference }}</el-descriptions-item>
          <el-descriptions-item label="实际显示">{{ actualTheme }}</el-descriptions-item>
          <el-descriptions-item label="主色调">
            <span class="color-value">{{ primaryColor }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { getCurrentTheme, isDarkMode, isSystemDarkMode } from '@/utils/theme'

const form = reactive({
  username: '',
  password: '',
  gender: 'male'
})

const currentTheme = ref(getCurrentTheme())

const currentThemeText = computed(() => {
  const themeMap = {
    light: '亮色模式',
    dark: '暗色模式',
    auto: '跟随系统'
  }
  return themeMap[currentTheme.value]
})

const systemPreference = computed(() => {
  return isSystemDarkMode() ? '暗色模式' : '亮色模式'
})

const actualTheme = computed(() => {
  return isDarkMode() ? '暗色模式' : '亮色模式'
})

const primaryColor = computed(() => {
  return getComputedStyle(document.documentElement).getPropertyValue('--el-color-primary').trim()
})

const showMessage = (type: 'success' | 'warning' | 'error' | 'info') => {
  const messages = {
    success: '这是一条成功消息！',
    warning: '这是一条警告消息！',
    error: '这是一条错误消息！',
    info: '这是一条信息消息！'
  }
  
  ElMessage({
    type,
    message: messages[type]
  })
}

onMounted(() => {
  // 监听主题变化
  const observer = new MutationObserver(() => {
    currentTheme.value = getCurrentTheme()
  })
  
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme', 'class']
  })
})
</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.theme-demo-container {
  padding: $spacing-lg;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h2 {
    margin: 0;
    color: var(--el-text-color-primary);
  }
}

.demo-section {
  margin-bottom: $spacing-xl;
  
  h3 {
    margin-bottom: $spacing-md;
    color: var(--el-text-color-primary);
  }
  
  h4 {
    margin-bottom: $spacing-sm;
    color: var(--el-text-color-regular);
  }
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  
  span {
    margin-top: $spacing-sm;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.color-box {
  width: 60px;
  height: 60px;
  border-radius: $border-radius-base;
  border: 1px solid var(--el-border-color);
  
  &.primary { background-color: var(--el-color-primary); }
  &.success { background-color: var(--el-color-success); }
  &.warning { background-color: var(--el-color-warning); }
  &.danger { background-color: var(--el-color-danger); }
  &.info { background-color: var(--el-color-info); }
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-lg;
}

.component-item {
  padding: $spacing-md;
  border: 1px solid var(--el-border-color);
  border-radius: $border-radius-base;
  background-color: var(--el-bg-color-overlay);
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.message-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.color-value {
  font-family: monospace;
  background-color: var(--el-fill-color-light);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
