// 强制主题覆盖样式 - 使用最高优先级
@import './variables.scss';

// 使用更高的选择器优先级来强制覆盖 Element Plus 样式
html {
  // 主色调相关的 CSS 变量
  --el-color-primary: #{$primary-color} !important;
  --el-color-primary-rgb: #{red($primary-color), green($primary-color), blue($primary-color)} !important;
  
  // 主色调的各种变体
  --el-color-primary-light-3: #{mix(#ffffff, $primary-color, 30%)} !important;
  --el-color-primary-light-5: #{mix(#ffffff, $primary-color, 50%)} !important;
  --el-color-primary-light-7: #{mix(#ffffff, $primary-color, 70%)} !important;
  --el-color-primary-light-8: #{mix(#ffffff, $primary-color, 80%)} !important;
  --el-color-primary-light-9: #{mix(#ffffff, $primary-color, 90%)} !important;
  --el-color-primary-dark-2: #{mix(#000000, $primary-color, 20%)} !important;
}

// 强制覆盖所有主要的 Element Plus 组件
html .el-button--primary {
  background-color: #{$primary-color} !important;
  border-color: #{$primary-color} !important;
  color: #ffffff !important;
  
  &:hover {
    background-color: #{mix(#ffffff, $primary-color, 20%)} !important;
    border-color: #{mix(#ffffff, $primary-color, 20%)} !important;
  }
  
  &:active {
    background-color: #{mix(#000000, $primary-color, 10%)} !important;
    border-color: #{mix(#000000, $primary-color, 10%)} !important;
  }
  
  &:focus {
    background-color: #{$primary-color} !important;
    border-color: #{$primary-color} !important;
  }
}

html .el-button--primary.is-disabled {
  background-color: #{mix(#ffffff, $primary-color, 50%)} !important;
  border-color: #{mix(#ffffff, $primary-color, 50%)} !important;
}

// 链接颜色
html .el-link--primary {
  color: #{$primary-color} !important;
  
  &:hover {
    color: #{mix(#ffffff, $primary-color, 20%)} !important;
  }
}

// 菜单激活状态
html .el-menu-item.is-active {
  color: #{$primary-color} !important;
}

html .el-menu--horizontal .el-menu-item.is-active {
  border-bottom-color: #{$primary-color} !important;
}

// 复选框
html .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #{$primary-color} !important;
  border-color: #{$primary-color} !important;
}

html .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #{$primary-color} !important;
  border-color: #{$primary-color} !important;
}

// 单选框
html .el-radio__input.is-checked .el-radio__inner {
  border-color: #{$primary-color} !important;
  background: #{$primary-color} !important;
}

html .el-radio__input.is-checked .el-radio__inner::after {
  background-color: #ffffff !important;
}

// 输入框焦点
html .el-input__wrapper.is-focus {
  border-color: #{$primary-color} !important;
  box-shadow: 0 0 0 1px #{$primary-color} inset !important;
}

html .el-input.is-focus .el-input__wrapper {
  border-color: #{$primary-color} !important;
  box-shadow: 0 0 0 1px #{$primary-color} inset !important;
}

// 开关
html .el-switch.is-checked .el-switch__core {
  background-color: #{$primary-color} !important;
  border-color: #{$primary-color} !important;
}

// 进度条
html .el-progress-bar__inner {
  background-color: #{$primary-color} !important;
}

// 标签页
html .el-tabs__active-bar {
  background-color: #{$primary-color} !important;
}

html .el-tabs__item.is-active {
  color: #{$primary-color} !important;
}

// 分页器
html .el-pagination .el-pager li.is-active {
  background-color: #{$primary-color} !important;
  color: #ffffff !important;
}

html .el-pagination .btn-next:hover,
html .el-pagination .btn-prev:hover {
  color: #{$primary-color} !important;
}

// 下拉菜单
html .el-dropdown-menu__item:hover {
  background-color: #{mix(#ffffff, $primary-color, 90%)} !important;
  color: #{$primary-color} !important;
}

// 表格
html .el-table__header th.is-sortable:hover {
  background-color: #{mix(#ffffff, $primary-color, 95%)} !important;
}

html .el-table tbody tr:hover > td {
  background-color: #{mix(#ffffff, $primary-color, 95%)} !important;
}

// 日期选择器
html .el-date-table td.today .el-date-table-cell__text {
  color: #{$primary-color} !important;
}

html .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  background-color: #{$primary-color} !important;
  color: #ffffff !important;
}

// 时间选择器
html .el-time-panel__btn.confirm {
  color: #{$primary-color} !important;
}

// 滑块
html .el-slider__runway .el-slider__bar {
  background-color: #{$primary-color} !important;
}

html .el-slider__button {
  border-color: #{$primary-color} !important;
}

// 评分
html .el-rate__icon.is-active {
  color: #{$primary-color} !important;
}

// 颜色选择器
html .el-color-picker__trigger {
  border-color: #{$primary-color} !important;
}

// 穿梭框
html .el-transfer-panel__header .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #{$primary-color} !important;
  border-color: #{$primary-color} !important;
}

// 树形控件
html .el-tree-node__content:hover {
  background-color: #{mix(#ffffff, $primary-color, 95%)} !important;
}

html .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #{mix(#ffffff, $primary-color, 90%)} !important;
  color: #{$primary-color} !important;
}

// 暗色模式下的调整
html[data-theme="dark"] {
  --el-color-primary: #{lighten($primary-color, 15%)} !important;
  --el-color-primary-light-3: #{mix(#ffffff, lighten($primary-color, 15%), 30%)} !important;
  --el-color-primary-light-5: #{mix(#ffffff, lighten($primary-color, 15%), 50%)} !important;
  --el-color-primary-light-7: #{mix(#ffffff, lighten($primary-color, 15%), 70%)} !important;
  --el-color-primary-light-8: #{mix(#ffffff, lighten($primary-color, 15%), 80%)} !important;
  --el-color-primary-light-9: #{mix(#ffffff, lighten($primary-color, 15%), 90%)} !important;
  --el-color-primary-dark-2: #{mix(#000000, lighten($primary-color, 15%), 20%)} !important;
}

html[data-theme="dark"] .el-button--primary {
  background-color: #{lighten($primary-color, 15%)} !important;
  border-color: #{lighten($primary-color, 15%)} !important;
  
  &:hover {
    background-color: #{lighten($primary-color, 25%)} !important;
    border-color: #{lighten($primary-color, 25%)} !important;
  }
}
