<template>
  <el-card class="demo-component">
    <template #header>
      <div class="card-header">
        <span>组件示例</span>
        <font-awesome-icon :icon="['fas', 'cog']" />
      </div>
    </template>
    
    <div class="demo-content">
      <!-- Element Plus 组件示例 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-button type="primary" @click="showMessage">
            <font-awesome-icon :icon="['fas', 'user']" />
            显示消息
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-switch v-model="switchValue" />
        </el-col>
      </el-row>
      
      <el-divider />
      
      <el-form :model="form" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="form.username" placeholder="请输入用户名">
            <template #prefix>
              <font-awesome-icon :icon="['fas', 'user']" />
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="密码">
          <el-input 
            v-model="form.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
          >
            <template #prefix>
              <font-awesome-icon :icon="['fas', 'lock']" />
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const switchValue = ref(false)

const form = reactive({
  username: '',
  password: ''
})

const showMessage = () => {
  ElMessage.success('这是一个成功消息！')
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.demo-component {
  margin: $spacing-md 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-content {
  .el-row {
    margin-bottom: $spacing-md;
  }
}
</style>
